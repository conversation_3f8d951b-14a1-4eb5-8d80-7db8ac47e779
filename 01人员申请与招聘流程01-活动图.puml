@startuml

title 人员申请与招聘录用流程

' --- 外观参数设置 (Skinparams) ---
!theme materia
skinparam shadowing false
skinparam defaultFontName "SimSun, NSimSun" 
skinparam defaultFontSize 16
skinparam title {
    FontSize 30
    FontColor Black
}
skinparam activity {
    BorderColor #2C3E50
    BackgroundColor #FFFFFF
    ArrowColor #2C3E50
    FontColor #000000 
    DiamondBorderColor #D35400
    DiamondBackgroundColor #F1C40F
    DiamondFontColor #000000
}
skinparam note {
    BorderColor #2C3E50
    BackgroundColor #FFFFE0
    FontColor #000000
}
skinparam partition {
    BorderColor #A9A9A9
    BackgroundColor Transparent
    FontColor Blue
    FontSize 16
}
skinparam roundcorner 20

' --- 自动编号变量 ---
!$step = 0 
!function $next_step()
  !$step = $step + 1 
  !return $step
!endfunction

' --- 流程开始 (定义所有泳道) ---

|#E6F3E6|用人部门|
|#F5F5DC|区域总经理|
|#FFAACD|集团总经理|


|#E6E6FA|行政人事部|
|#FFFACD|候选人|
|#E0FFFF|IT组|

|用人部门|
start

-><size:40><color:grey>需求与审批</color></size>;
    :$next_step(). 产生用人需求;
    note right
      <b>需求来源:</b>
      - 业务扩张 / 新项目启动
      - 人员离职 / 内部异动
      - 年度招聘计划
    end note
    repeat
    repeat
    :$next_step(). 提交《人员需求申请单》;
  |区域总经理|
    backward:$next_step(). 说明原因;
     repeat while ($next_step() .区域审批) is (<color:red>驳回</color>) not (<color:green>同意</color>)
      
       floating note right
      <b>驳回条件:</b>
      - 岗位是否在年度编制内
      - 岗位描述(JD)是否清晰
      - 薪酬范围是否合理
    end note
   
    if ($next_step().根据岗位级别，\n是否需要集团审批?) then (<color:green>Yes</color>)
        
    else (<color:red>No</color>)
        #palegreen:(A)
    endif
|集团总经理|
     repeat while ($next_step() .集团审批) is (<color:red>驳回</color>) not (<color:green>同意</color>)
     #palegreen:(A)
    detach

    |行政人事部|
    #palegreen:(A)
    if ($next_step(). 适合内部人选?) then (<color:green>Yes</color>)

        else (<color:red>No</color>)
         |行政人事部|
         #palegreen:(C)
        Detach
    endif
    |用人部门|
    repeat
    repeat
        :$next_step(). 内部人选审核;
|区域总经理|
    backward:$next_step(). 说明原因;
     repeat while ($next_step() .区域审批) is (<color:red>驳回</color>) not (<color:green>同意</color>)

    if ($next_step().根据岗位级别，\n是否需要集团审批?) then (<color:green>Yes</color>)
        
    else (<color:red>No</color>)
        #palegreen:(B)
    endif
|集团总经理|
     repeat while ($next_step() .集团审批) is (<color:red>驳回</color>) not (<color:green>同意</color>)
                  #palegreen:(B)
        Detach

|行政人事部|
     #palegreen:(B)
    :$next_step(). 办理内部调动手续;   
    -[#grey,dashed]->
    end

     |行政人事部|
     #palegreen:(C)
        #lightgrey:$next_step(). 确认启动外部招聘;
  -><size:40><color:grey>招聘与简历筛选</color></size>;
    :$next_step(). 在各渠道发布职位;
    note right: 内部推荐、招聘网站、\n社交媒体等
    :$next_step(). 筛选简历，建立候选人清单;
    :$next_step(). 推荐候选人简历给用人部门;

-><size:40><color:grey>面试与评估</color></size>;
    |用人部门|
    :$next_step(). 反馈筛选意见，\n确定面试名单;
    |行政人事部|
    :$next_step(). 电话/邮件沟通，邀约面试;
    |候选人|
    :$next_step(). 确认面试时间;
    |行政人事部|
    :$next_step(). 进行HR面试;
    repeat
    repeat
    |用人部门|
    :$next_step(). 进行业务/技术面试;
|区域总经理|
    backward:$next_step(). 说明原因;
     repeat while ($next_step() .区域审批) is (<color:red>驳回</color>) not (<color:green>同意</color>)

    if ($next_step().根据岗位级别，\n是否需要集团审批?) then (<color:green>Yes</color>)
        
    else (<color:red>No</color>)
        #palegreen:(D)
    endif
|集团总经理|
     repeat while ($next_step() .集团审批) is (<color:red>驳回</color>) not (<color:green>同意</color>)
                  #palegreen:(D)
        Detach


    |行政人事部|
    #palegreen:(D)
     :$next_step(). 进行薪酬审批与背景调查(如需);
     -><size:40><color:grey>Offer与入职准备</color></size>;
     :$next_step(). 发放《录用通知书》(Offer Letter);

    |候选人|
    if ($next_step().是否接受Offer?) then (<color:green>Yes</color>)
      #palegreen:$next_step(). 签署并回传Offer;
      :$next_step(). 提供个人基本信息;
    else  (<color:red>No</color>)
    -[#grey,dashed]->
       #lightgrey:$next_step().婉拒Offer;
        stop
    endif

    |行政人事部|
    :$next_step(). 收到回签Offer，\n通知各部门准备;

    ' --- 并行准备任务 ---
    !$parallel_step = $step + 1
    fork
        |IT组|
        :$parallel_step.c. 准备系统账号, \n确认权限,邮箱和设备; 

    fork again
        |用人部门|
        :$parallel_step.a. 准备工位及办公用品;
    fork again
        |行政人事部|
        :$parallel_step.b. 准备薪酬和社保档案;
    end fork
    !$step = $step + 1 

-><size:40><color:grey>正式入职</color></size>;
    |候选人|
    :$next_step(). 按时报到;

    |行政人事部|
    :$next_step(). 办理入职手续;
   floating note right #FFC0CB
      <font color=red><b>核心痛点:</b></font>
      1. 员工需重复填写大量纸质表格。
      2. HR需手动将纸质信息录入多个系统。
      3. 部门间信息传递效率低且易遗漏。
    end note
    :$next_step(). 签署《劳动合同》及其他文件;
    :$next_step(). 引导员工至工位;

    |用人部门|
    :$next_step(). 接收新员工;
end 
@enduml