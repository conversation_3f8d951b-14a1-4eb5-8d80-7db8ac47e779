CREATE TABLE `组织单元表` (
	`组织单元ID` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`部门名称` VARCHAR(255),
	`区域名称` VARCHAR(50),
	`组别名称` VARCHAR(255),
	`线别名称` VARCHAR(255),
	PRIMARY KEY(`组织单元ID`)
);


CREATE TABLE `员工详细信息表` (
	`员工编号` INTEGER NOT NULL UNIQUE,
	`国籍` VARCHAR(255),
	`民族` VARCHAR(255),
	`员工出生地` VARCHAR(255),
	`婚姻状态` BOOLEAN,
	`配偶工作状态` BOOLEAN,
	`子女数量` TINYINT,
	`员工怀孕日期` DATE,
	`员工产假开始日期` DATE,
	`员工产假结束日期` DATE,
	`子女生日1` DATE,
	`子女生日2` DATE,
	`员工工会名称` VARCHAR(255),
	`社保卡编号` VARCHAR(255),
	`最新体检日期` DATE,
	PRIMARY KEY(`员工编号`)
);


CREATE TABLE `奖惩金额` (
	`奖惩编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工编号` INTEGER,
	`奖惩金额` DECIMAL,
	`奖惩日期` DATE,
	`奖惩规则编号` VARCHAR(50) NOT NULL,
	`备注` VARCHAR(20),
	PRIMARY KEY(`奖惩编号`)
);


CREATE TABLE `奖惩记录` (
	`奖惩编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工编号` INTEGER,
	`考核期` DATE,
	`考核项目` VARBINARY(255) NOT NULL,
	`考核结果` VARCHAR(255),
	`奖惩类型` VARCHAR(255),
	`奖惩区间` VARCHAR(255),
	`奖惩金额` DECIMAL,
	`有效状态` VARCHAR(20) NOT NULL,
	`备注` TEXT,
	PRIMARY KEY(`奖惩编号`)
);


CREATE TABLE `奖惩规则` (
	`奖惩规则编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`奖惩类型` VARCHAR(50) NOT NULL,
	`奖惩区间` DECIMAL,
	`奖惩金额` DECIMAL,
	`适用职位` VARCHAR(255),
	`出勤条件` VARCHAR(255),
	`生效日期` DATE,
	PRIMARY KEY(`奖惩规则编号`)
);


CREATE TABLE `正常出勤工资` (
	`正常出勤工资编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工编号` INTEGER,
	`工资月份` VARCHAR(7) NOT NULL,
	`工资金额` VARCHAR(50) NOT NULL,
	`考勤规则编号` VARCHAR(255),
	PRIMARY KEY(`正常出勤工资编号`)
);


CREATE TABLE `加班工资` (
	`加班编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工编号` INTEGER,
	`加班类别` VARCHAR(255),
	`加班时数` DECIMAL,
	`金额` DECIMAL,
	`加班规则编号` VARCHAR(50) NOT NULL,
	PRIMARY KEY(`加班编号`)
);


CREATE TABLE `福利金额` (
	`福利编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工编号` INTEGER,
	`金额` DECIMAL,
	`开始日期` DATE,
	`结束日期` DATE,
	`状态` VARCHAR(20),
	`福利规则编号` VARCHAR(50) NOT NULL,
	PRIMARY KEY(`福利编号`)
);


CREATE TABLE `考勤规则` (
	`考勤规则编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`规则名称` VARCHAR(50) NOT NULL,
	`上班时间` TIME,
	`下班时间` TIME,
	`迟到扣款规则` DECIMAL,
	`缺勤扣款规则` DECIMAL,
	PRIMARY KEY(`考勤规则编号`)
);


CREATE TABLE `岗位主数据表` (
	`所属组织单元ID` INTEGER,
	`岗位编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`上级岗位编号` INTEGER,
	`岗位名称` VARCHAR(50) NOT NULL,
	`管理成本分类` VARCHAR(255),
	`每月应加班时数` INTEGER,
	`是否适用加班费` BOOLEAN,
	`是否适用话费补助` BOOLEAN,
	`是否适用车费补助` BOOLEAN,
	`是否适用住房公积金` BOOLEAN,
	`是否适合午餐费` BOOLEAN,
	`是否适合加班餐费` BOOLEAN,
	`是否适合全勤奖` BOOLEAN,
	`是否适合绩效奖` BOOLEAN,
	PRIMARY KEY(`岗位编号`)
);


CREATE TABLE `员工主数据表` (
	`部门编号` INTEGER,
	`员工编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工姓名` VARCHAR(100) NOT NULL,
	`性别` VARCHAR(10),
	`出生日期` DATE,
	`入职日期` DATE,
	`职位编号` INTEGER,
	`邮箱` VARCHAR(100),
	`电话` VARCHAR(20),
	`状态` VARCHAR(20),
	`地址` TEXT,
	`身份证号` VARCHAR(50),
	PRIMARY KEY(`员工编号`)
);


CREATE TABLE `考勤记录` (
	`考勤编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工编号` INTEGER,
	`考勤日期` DATE NOT NULL,
	`打卡时间` TIME,
	`有效状态` VARCHAR(20) NOT NULL,
	`备注` TEXT,
	PRIMARY KEY(`考勤编号`)
);


CREATE TABLE `加班规则` (
	`加班规则编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`加班日期` DATE,
	`开始时间` TIME,
	`结束时间` TIME,
	`加班费率` DECIMAL,
	PRIMARY KEY(`加班规则编号`)
);


CREATE TABLE `任职薪酬记录` (
	`工资编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工编号` INTEGER,
	`工资月份` VARCHAR(7) NOT NULL,
	`正常出勤工资` DECIMAL NOT NULL,
	`福利金汇总` DECIMAL,
	`奖惩金汇总` DECIMAL DEFAULT 0,
	`加班工资汇总` DECIMAL DEFAULT 0,
	`薪资扣除汇总` DECIMAL DEFAULT 0,
	`实发工资` DECIMAL,
	`状态` VARCHAR(20),
	`发放日期` DATE,
	`任职记录ID` VARCHAR(255),
	PRIMARY KEY(`工资编号`)
);


CREATE TABLE `薪资扣除` (
	`扣除编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工编号` INTEGER,
	`工资月份` VARCHAR(7) NOT NULL,
	`扣除类型` VARCHAR(50) NOT NULL,
	`扣除金额` DECIMAL NOT NULL,
	`扣除说明` TEXT,
	`考勤规则编号` VARCHAR(255),
	PRIMARY KEY(`扣除编号`)
);


CREATE TABLE `福利规则` (
	`福利规则编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`福利类型` VARCHAR(50) NOT NULL,
	`公司缴纳比例` DECIMAL,
	`员工缴纳比例` DECIMAL,
	`缴纳基数` DECIMAL,
	`生效日期` DATE,
	PRIMARY KEY(`福利规则编号`)
);


CREATE TABLE `员工合同表` (
	`员工编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`员工合同ID` INTEGER,
	`合同类型` VARCHAR(255),
	`合同开始日期` DATE,
	`合同到期日期` DATE,
	`合同续签标志` DATE,
	`合同补偿金比例` DECIMAL,
	PRIMARY KEY(`员工编号`)
);


CREATE TABLE `字段权限规则` (
	`字段权限ID` JSON NOT NULL UNIQUE,
	`数据库名` VARCHAR(255),
	`数据表名` VARCHAR(255),
	`数据表名` VARCHAR(255),
	`是否允许浏览` BOOLEAN,
	`是否允许修改` BOOLEAN,
	`权限确定日期` DATE,
	`权限确定员工编号` JSON,
	PRIMARY KEY(`字段权限ID`)
);


CREATE TABLE `部门说明表` (
	`部门名称` VARCHAR(255) NOT NULL UNIQUE,
	`区域名称` VARCHAR(255),
	`中心名称` VARCHAR(255),
	`部门类别` VARCHAR(255),
	`定义说明` TEXT(65535),
	PRIMARY KEY(`部门名称`)
);


CREATE TABLE `职级分类表` (
	`职级ID` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`岗位名称` VARCHAR(255),
	`职级名称` VARCHAR(255),
	`职级津贴起始金额` INTEGER,
	`职级津贴最高金额` INTEGER,
	`津贴币种` VARCHAR(255),
	PRIMARY KEY(`职级ID`)
);


CREATE TABLE `岗位编制表` (
	`编制ID` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`编制年度` YEAR,
	`岗位编号` VARCHAR(255),
	`组织单元ID` INTEGER,
	`计划编制数` TINYINT,
	`编制类型` VARCHAR(255),
	`编制状态` VARCHAR(255),
	PRIMARY KEY(`编制ID`)
);


CREATE TABLE `任职记录表` (
	`员工编号` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`任职记录ID` VARCHAR(255),
	`任职开始日期` DATE,
	`任职结束日期` DATE,
	`岗位编号` VARCHAR(255),
	`任职类型` VARCHAR(255),
	`用工类型` VARCHAR(255),
	PRIMARY KEY(`员工编号`)
);


CREATE TABLE `角色权限表` (
	`角色名称` VARCHAR(255),
	`角色职责说明` TEXT(65535),
	`角色权限集合` JSON,
	`角色编号` VARCHAR(255) NOT NULL UNIQUE,
	PRIMARY KEY(`角色编号`)
);


CREATE TABLE `岗位角色关联表` (
	`关联ID` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`角色编号` VARCHAR(255),
	`关联类型` VARCHAR(255),
	`关联日期` VARCHAR(255),
	`岗位编号` VARCHAR(255),
	`关联操作人` VARCHAR(255),
	PRIMARY KEY(`关联ID`)
);


CREATE TABLE `用户员工关联表` (
	`关联id` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
	`用户名称` VARCHAR(255),
	`员工编号` INTEGER,
	`关联日期` DATE,
	`关联操作员工编号` INTEGER,
	`关联说明` TEXT(65535),
	`用户密码` VARCHAR(255),
	PRIMARY KEY(`关联id`)
);


ALTER TABLE `岗位主数据表`
ADD FOREIGN KEY(`上级岗位编号`) REFERENCES `岗位主数据表`(`岗位编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `考勤记录`
ADD FOREIGN KEY(`员工编号`) REFERENCES `员工主数据表`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `福利金额`
ADD FOREIGN KEY(`员工编号`) REFERENCES `员工主数据表`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `薪资扣除`
ADD FOREIGN KEY(`员工编号`) REFERENCES `员工主数据表`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `福利规则`
ADD FOREIGN KEY(`福利规则编号`) REFERENCES `福利金额`(`福利规则编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `薪资扣除`
ADD FOREIGN KEY(`考勤规则编号`) REFERENCES `考勤规则`(`考勤规则编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `加班规则`
ADD FOREIGN KEY(`加班规则编号`) REFERENCES `加班工资`(`加班规则编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `员工主数据表`
ADD FOREIGN KEY(`员工编号`) REFERENCES `任职薪酬记录`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `员工主数据表`
ADD FOREIGN KEY(`员工编号`) REFERENCES `加班工资`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `考勤规则`
ADD FOREIGN KEY(`考勤规则编号`) REFERENCES `正常出勤工资`(`考勤规则编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `员工主数据表`
ADD FOREIGN KEY(`员工编号`) REFERENCES `正常出勤工资`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `奖惩记录`
ADD FOREIGN KEY(`员工编号`) REFERENCES `员工主数据表`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `员工主数据表`
ADD FOREIGN KEY(`员工编号`) REFERENCES `奖惩金额`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `奖惩记录`
ADD FOREIGN KEY(`奖惩编号`) REFERENCES `奖惩金额`(`奖惩编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `奖惩记录`
ADD FOREIGN KEY(`奖惩类型`) REFERENCES `奖惩规则`(`奖惩类型`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `奖惩记录`
ADD FOREIGN KEY(`奖惩区间`) REFERENCES `奖惩规则`(`奖惩区间`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `奖惩金额`
ADD FOREIGN KEY(`奖惩规则编号`) REFERENCES `奖惩规则`(`奖惩规则编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `奖惩记录`
ADD FOREIGN KEY(`奖惩金额`) REFERENCES `奖惩金额`(`奖惩金额`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `员工详细信息表`
ADD FOREIGN KEY(`员工编号`) REFERENCES `员工主数据表`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `员工合同表`
ADD FOREIGN KEY(`员工编号`) REFERENCES `员工主数据表`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `组织单元表`
ADD FOREIGN KEY(`组织单元ID`) REFERENCES `岗位主数据表`(`所属组织单元ID`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `组织单元表`
ADD FOREIGN KEY(`部门名称`) REFERENCES `部门说明表`(`部门名称`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `岗位主数据表`
ADD FOREIGN KEY(`岗位名称`) REFERENCES `职级分类表`(`岗位名称`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `岗位编制表`
ADD FOREIGN KEY(`组织单元ID`) REFERENCES `组织单元表`(`组织单元ID`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `任职记录表`
ADD FOREIGN KEY(`员工编号`) REFERENCES `员工合同表`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `岗位编制表`
ADD FOREIGN KEY(`岗位编号`) REFERENCES `任职记录表`(`岗位编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `岗位编制表`
ADD FOREIGN KEY(`岗位编号`) REFERENCES `岗位角色关联表`(`岗位编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `岗位角色关联表`
ADD FOREIGN KEY(`角色编号`) REFERENCES `角色权限表`(`角色编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `角色权限表`
ADD FOREIGN KEY(`角色权限集合`) REFERENCES `字段权限规则`(`字段权限ID`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `用户员工关联表`
ADD FOREIGN KEY(`员工编号`) REFERENCES `员工主数据表`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE `用户员工关联表`
ADD FOREIGN KEY(`关联操作员工编号`) REFERENCES `员工主数据表`(`员工编号`)
ON UPDATE NO ACTION ON DELETE NO ACTION;