# agent.py 代码功能和实现逻辑分析

## 📋 概述

`agent.py` 是一个基于 **ReAct（Reasoning and Acting）** 模式的智能代理实现，它能够通过思考-行动-观察的循环模式来完成复杂任务。该代理集成了 OpenAI 的 GPT-4 模型，并提供了文件操作和终端命令执行等工具。

## 🏗️ 核心架构

### 主要类：ReActAgent

```python
class ReActAgent:
    def __init__(self, tools: List[Callable], model: str, project_directory: str):
        self.tools = { func.__name__: func for func in tools }
        self.model = model
        self.project_directory = project_directory
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=ReActAgent.get_api_key(),
        )
```

## 🔄 核心工作流程

### 1. ReAct 循环执行逻辑

```python
def run(self, user_input: str):
    messages = [
        {"role": "system", "content": self.render_system_prompt(react_system_prompt_template)},
        {"role": "user", "content": f"<question>{user_input}</question>"}
    ]

    while True:
        # 请求模型
        content = self.call_model(messages)
        
        # 检测 Thought
        thought_match = re.search(r"<thought>(.*?)</thought>", content, re.DOTALL)
        
        # 检测模型是否输出 Final Answer
        if "<final_answer>" in content:
            final_answer = re.search(r"<final_answer>(.*?)</final_answer>", content, re.DOTALL)
            return final_answer.group(1)
        
        # 检测 Action 并执行
        action_match = re.search(r"<action>(.*?)</action>", content, re.DOTALL)
```

### 2. 工作流程步骤

1. **💭 Thought（思考）**：模型分析当前情况并制定计划
2. **🔧 Action（行动）**：执行具体的工具函数
3. **🔍 Observation（观察）**：获取工具执行结果
4. **🔄 循环**：基于观察结果继续思考，直到得出最终答案

## 🛠️ 内置工具函数

### 文件操作工具

| 工具名称 | 功能描述 | 参数 |
|---------|---------|------|
| `read_file` | 读取文件内容 | `file_path`: 文件路径 |
| `write_to_file` | 写入文件内容 | `file_path`: 文件路径<br>`content`: 写入内容 |
| `run_terminal_command` | 执行终端命令 | `command`: 终端命令 |

### 安全机制

```python
# 只有终端命令才需要询问用户，其他的工具直接执行
should_continue = input(f"\n\n是否继续？（Y/N）") if tool_name == "run_terminal_command" else "y"
if should_continue.lower() != 'y':
    print("\n\n操作已取消。")
    return "操作被用户取消"
```

## 🔧 关键技术实现

### 1. 动作解析器

```python
def parse_action(self, code_str: str) -> Tuple[str, List[str]]:
    match = re.match(r'(\w+)\((.*)\)', code_str, re.DOTALL)
    if not match:
        raise ValueError("Invalid function call syntax")
    
    func_name = match.group(1)
    args_str = match.group(2).strip()
```

**特点**：
- 支持复杂的参数解析，包括多行字符串
- 处理嵌套括号和引号
- 自动类型转换（使用 `ast.literal_eval`）

### 2. 系统提示模板渲染

```python
def render_system_prompt(self, system_prompt_template: str) -> str:
    """渲染系统提示模板，替换变量"""
    tool_list = self.get_tool_list()
    file_list = ", ".join(
        os.path.abspath(os.path.join(self.project_directory, f))
        for f in os.listdir(self.project_directory)
    )
    return Template(system_prompt_template).substitute(
        operating_system=self.get_operating_system_name(),
        tool_list=tool_list,
        file_list=file_list
    )
```

**动态信息注入**：
- 🖥️ 操作系统信息
- 🛠️ 可用工具列表
- 📁 项目目录文件列表

### 3. API 配置管理

```python
@staticmethod
def get_api_key() -> str:
    """Load the API key from an environment variable."""
    load_dotenv()
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        raise ValueError("未找到 OPENROUTER_API_KEY 环境变量，请在 .env 文件中设置。")
    return api_key
```

## 🚀 使用方式

### 命令行启动

```bash
python agent.py <project_directory>
```

### 主要依赖

- **openai**: OpenAI API 客户端
- **click**: 命令行界面框架
- **dotenv**: 环境变量管理
- **platform**: 系统信息获取

## 🎯 设计亮点

### 1. **模块化设计**
- 工具函数可灵活扩展
- 清晰的职责分离

### 2. **安全性考虑**
- 终端命令执行需用户确认
- 异常处理机制完善

### 3. **智能解析**
- 复杂参数解析算法
- 支持多种数据类型

### 4. **用户体验**
- 实时显示思考过程
- 清晰的执行状态反馈
- 中文界面友好

## � 实际案例演示

为了更好地理解 ReAct 循环中信息传递的过程，以下是一个简单的案例演示：

### 用户任务
```
"请帮我创建一个名为 hello.txt 的文件，内容是 'Hello, World!'"
```

### 信息传递流程

#### 第1轮循环

**🔄 输入消息**：
```json
[
  {"role": "system", "content": "系统提示（包含工具列表、文件列表等）"},
  {"role": "user", "content": "<question>请帮我创建一个名为 hello.txt 的文件，内容是 'Hello, World!'</question>"}
]
```

**🤖 模型响应**：
```xml
<thought>
用户想要创建一个文件，我需要使用 write_to_file 工具来完成这个任务。
参数应该是文件路径 "hello.txt" 和内容 "Hello, World!"
</thought>

<action>
write_to_file("hello.txt", "Hello, World!")
</action>
```

**🔧 工具执行**：
- 解析动作：`write_to_file("hello.txt", "Hello, World!")`
- 执行结果：`"写入成功"`

**📝 消息更新**：
```json
[
  {"role": "system", "content": "系统提示"},
  {"role": "user", "content": "<question>请帮我创建一个名为 hello.txt 的文件，内容是 'Hello, World!'</question>"},
  {"role": "assistant", "content": "<thought>用户想要创建一个文件...</thought>\n<action>write_to_file(\"hello.txt\", \"Hello, World!\")</action>"},
  {"role": "user", "content": "<observation>写入成功</observation>"}
]
```

#### 第2轮循环

**🤖 模型响应**：
```xml
<thought>
文件已经成功创建，任务完成。我应该给用户一个确认信息。
</thought>

<final_answer>
已成功创建文件 hello.txt，内容为 "Hello, World!"。文件创建完成！
</final_answer>
```

**✅ 任务完成**：返回最终答案并结束循环

### 关键信息传递点

1. **📥 用户输入** → **🧠 系统提示模板渲染** → **💭 模型思考**
2. **💭 模型思考** → **🔧 动作解析** → **⚙️ 工具执行**
3. **⚙️ 工具执行结果** → **📝 观察记录** → **🔄 下一轮思考**
4. **🔄 循环继续** 直到 **✅ 最终答案**

### 消息历史的作用

- **上下文保持**：每轮对话都保留完整的历史记录
- **状态传递**：工具执行结果通过 `<observation>` 标签传递给模型
- **决策依据**：模型基于历史信息做出下一步决策

## �📝 总结

这是一个功能完整的 ReAct 智能代理实现，具备：

- ✅ **完整的 ReAct 循环**：思考→行动→观察
- ✅ **丰富的工具集**：文件操作、终端命令
- ✅ **安全机制**：用户确认、异常处理
- ✅ **灵活扩展**：易于添加新工具
- ✅ **用户友好**：中文界面、实时反馈

该代理特别适合用于自动化文件处理、代码生成、项目管理等需要多步骤推理和操作的复杂任务。
