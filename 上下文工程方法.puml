@startuml

title 统一架构：上下文工程（CE） + Agent 自动化 + MCP 标准化

' --- 外观参数设置 (Skinparams) ---
!theme materia
skinparam shadowing false
skinparam defaultFontName "SimSun, NSimSun"
skinparam defaultFontSize 16
skinparam title {
    FontSize 30
    FontColor Black
}
skinparam activity {
    BorderColor #2C3E50
    BackgroundColor #FFFFFF
    ArrowColor #2C3E50
    FontColor #000000
    DiamondBorderColor #D35400
    DiamondBackgroundColor #F1C40F
    DiamondFontColor #000000
}
skinparam note {
    BorderColor #2C3E50
    BackgroundColor #FFFFE0
    FontColor #000000
}
skinparam partition {
    BorderColor #A9A9A9
    BackgroundColor Transparent
    FontColor Blue
    FontSize 16
}
skinparam roundcorner 20

' --- 自动编号变量 ---
!$step = 0
!function $next_step()
  !$step = $step + 1
  !return $step
!endfunction

' --- 流程开始 (定义所有泳道) ---

|#E6F3E6|用户/系统|
|#E6E6FA|Agent 自动化执行|
|#FFFACD|Context Engineering 方法论|
|#E0FFFF|MCP 标准化协议|

|用户/系统|
start

-><size:40><color:grey>任务启动与解析</color></size>;
:$next_step(). 接收用户任务请求;
note right
  <b>任务类型:</b>
  - 代码生成
  - 文档分析
  - 问题解答
  - 数据处理
end note

|Agent 自动化执行|
:$next_step(). 解析任务需求;
:$next_step(). 启动上下文流水线调度;

-><size:40><color:grey>上下文工程流程</color></size>;

|Context Engineering 方法论|
:$next_step(). 信息获取\n(Acquisition);
note right
  <b>获取来源:</b>
  - 代码库检索
  - 文档扫描
  - 历史对话
  - 外部API
end note

:$next_step(). 信息选择\n(Selection);
note right
  <b>选择策略:</b>
  - 相关性评分
  - 重要性排序
  - 去重处理
end note

:$next_step(). 信息加工\n(Transformation);
note right
  <b>加工方式:</b>
  - 格式标准化
  - 内容压缩
  - 结构化处理
end note

:$next_step(). 信息路由\n(Routing);
note right
  <b>路由规则:</b>
  - 按优先级分配
  - 按类型分类
  - 按容量限制
end note

:$next_step(). 信息注入\n(Injection);
note right
  <b>注入方式:</b>
  - 系统提示注入
  - 上下文窗口填充
  - 动态模板替换
end note

|MCP 标准化协议|
:$next_step(). 应用格式定义标准;
:$next_step(). 处理元数据与依赖声明;
:$next_step(). 执行组件交互协议;

-><size:40><color:grey>执行与反馈</color></size>;

|Agent 自动化执行|
:$next_step(). 调用工具/模型;

if ($next_step(). 执行结果是否满足要求?) then (<color:green>Yes</color>)
    :$next_step(). 返回最终结果;
else (<color:red>No</color>)
    :$next_step(). 分析错误原因;
    :$next_step(). 调整上下文策略;
    -[#grey,dashed]-> :$next_step(). 重新启动流水线调度;
endif

|用户/系统|
:$next_step(). 接收处理结果;

floating note right #FFC0CB
  <font color=red><b>核心痛点:</b></font>
  1. 上下文窗口限制导致信息截断
  2. 不同组件间标准不统一
  3. 动态调整策略复杂度高
end note

end

@enduml
