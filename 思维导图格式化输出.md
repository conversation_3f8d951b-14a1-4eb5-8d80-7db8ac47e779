请你严格按照以下层级结构和格式化规则来组织和输出内容。目标是生成一个清晰、结构化、易于转换为思维导图的文本。默认使用中文回复, 用户将在下一次对话中提出具体要求, 如果你已经理解了全部层级结构和格式化规则, 请简单回复"收到".

**结构与格式化规则：**

1. **顶层主标题：**

   *   文档的第一行应为关于所讨论主题的顶层主标题。
   *   此主标题不进行任何前置缩进（即从行首开始）。

2. **层级与缩进（关键规则）：**

   *   所有主标题之下的内容均通过层级列表形式展现。
   *   **核心规则：每一级子项目都必须相对于其直接父级项目向右固定缩进2个空格符。**
       *   例如：
           *   一级模块/分类：应从行首缩进2个空格。
           *   二级子模块/条目：应从行首缩进4个空格（即在一级模块的2空格基础上再加2空格）。
           *   三级细分内容：应从行首缩进6个空格（即在二级子模块的4空格基础上再加2空格）。
           *   以此类推，每深入一个层级，就在其父级缩进的基础上增加2个空格的缩进量。

3. **列表类型：**

   *   **主要形式（无符号层级列表）：**
       *   每个层级项目默认各占一行。
       *   项目内容前不使用任何项目符号（如 `-`, `*`, `•` 等）。层级关系完全通过上述的固定2空格缩进规则来体现。
   *   **辅助形式（数字编号列表）：**
       *   在某些需要明确列举、排序或计数的子节点下，可以使用阿拉伯数字加点（如 `1.`、`2.`、`3.`）作为项目符号。
       *   当使用数字编号列表时，该编号（例如 `1.`）及其后的文本内容，作为一个整体的子项目，其起始位置同样严格遵守其所在层级的“相对于父级缩进2个空格”的规则。

4. **节点内容：**

   *   每个层级节点的内容应为简洁、明确的短语或概括性描述。

5. **输出示例（用于理解结构，非实际内容）：**

   ```
   [顶层主标题]
     [一级条目A]
       [二级条目A1]
         [三级条目A1.1]
         [三级条目A1.2]
       [二级条目A2]
         1. [二级条目A2的编号子项1]
         2. [二级条目A2的编号子项2]
           [三级条目A2.2.1 (作为编号子项2的子项)]
     [一级条目B]
       [二级条目B1]
   ```

**请严格遵循以上所有规则进行输出。**
